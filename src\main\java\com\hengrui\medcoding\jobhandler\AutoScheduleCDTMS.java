/*
package com.hengrui.medcoding.jobhandler;

import com.hengrui.medcoding.service.DownloadMedcoding;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

*/
/**
 * @ClassName AutoScheduleCDTMS
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/10 9:10
 * @Version 1.0
 **//*


@Component
@Slf4j
public class AutoScheduleCDTMS {

    @Autowired
    DownloadMedcoding downloadMedcoding;

    @XxlJob("scheduleMedCoding")
    @Transactional
    public void scheduleCDTMSHandler() {
        downloadMedcoding.downloadAllCodingFile(new String[0]);
    }
}
*/
