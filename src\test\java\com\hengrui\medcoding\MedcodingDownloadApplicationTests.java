package com.hengrui.medcoding;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.hengrui.medcoding.constant.MedCN;

import com.hengrui.medcoding.constant.SASOnlieConstant;
import com.hengrui.medcoding.mapper.MedcodingMapper;
import com.hengrui.medcoding.service.DownloadMedcoding;
import com.hengrui.medcoding.utils.CDTMSAPI;
import com.hengrui.medcoding.utils.FilesUtil;
import com.hengrui.medcoding.utils.MinioUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@SpringBootTest
@Slf4j
class MedcodingDownloadApplicationTests {
    @Autowired
    DownloadMedcoding downloadMedcoding;
    @Autowired
    MedcodingMapper medcodingMapper;


    @Autowired
    MinioUtil minioUtil;


    @Test
    void contextLoads() {
    }


    //获取批次信息
//    @Test
//    void testGetBatchInfo() {
//        List<Map<String, String>> whoDrugBatchInfo = medcodingMapper.getWhoDrugBatchInfo("SHR-6390-III-301");
//        for (int i = 0; i < whoDrugBatchInfo.size(); i++) {
//            System.out.println(String.valueOf(whoDrugBatchInfo.get(i).get("id")));
//            System.out.println(whoDrugBatchInfo.get(i).get("COL_VER"));
//            System.out.println(whoDrugBatchInfo.get(i).get("COL_SN"));
//        }
//    }
//
//    //获取taskid
//    @Test
//    void postTest() throws Exception {
//        String result = downloadMedcoding.getTaskId("https://clinical-tst.hengruipharma.com:4080/usersyn/funcstart",
//                "400D24DA64294523BAD02920BB6D9FDE", "SHR-1210-III-308", "GLOBALB3Mar20", "W-20210506-0010", 462487581, false);
//        JSONObject jsonObject = new JSONObject(result);
//        System.out.println(jsonObject.get("taskid"));
//        System.out.println(jsonObject.get("status"));
//    }
//
//    @Test
//    void testGetToken() throws JSONException {
//        String result = downloadMedcoding.getToken();
//        JSONObject jsonObject = new JSONObject(result);
//        System.out.println(jsonObject.get("token"));
//    }
//
//    //获取下载地址
//    @Test
//    void testGetUrl() throws JSONException {
//        String result = downloadMedcoding.getUrl("https://clinical-tst.hengruipharma.com:4080/usersyn/funcresult",
//                "400D24DA64294523BAD02920BB6D9FDE", "CDD9C3BDA5F6431DB35BE6D120425CA2");
//        JSONObject jsonObject = new JSONObject(result);
//        System.out.println(jsonObject.get("status"));
//        System.out.println(jsonObject.get("url"));
//    }
//
//    //获取token
//    @Test
//    void testHttpsGet() throws Exception {
//        String result = downloadMedcoding.httpsGetJson("https://clinical-tst.hengruipharma.com:4080/usersyn/gettoken?projectid=SHR-1701-II-205&secret=bioknow@228");
//        JSONObject jsonObject = new JSONObject(result);
//        System.out.println(jsonObject.get("token"));
//    }

    @Test
    void testGetCookie() throws IOException {
        String result = downloadMedcoding.getCookies("https://clinical.hengruipharma.com/login/login.do?time=1687226019136&loginId=hui.zhou.hz36%40hengrui.com&password=28e970734c3562855ace98b3acb37f12&code=bado&base64Code=X3OQLm5425s%3D");
        System.out.println(result);
    }

    //下载zip数据
    @Test
    void testdownloadZipFile() {
//        downloadMedcoding.downLoadZipFile("/dbplug/output/temp/shr-1210-iii-308_w-20210506-0010_%E6%97%A2%E5%BE%80%E5%8F%8A%E5%90%88%E5%B9%B6%E7%94%A8%E8%8D%AF_20230703_0929_cn_en.zip","2023-09-07");
    }


    @Test
    void testDownloadFileByStudyId() {
        downloadMedcoding.getWhoDrugBatchInfo("SHR-6390-III-301");
    }

    @Test
    void testDowloadMedFile() {
        String[] args = {"123", "456"};
        downloadMedcoding.downloadAllCodingFile(args);
    }

    //测试taskId匹配


//    @Test
//    void testUploadFileToMinio() {
//        File file = new File("C:\\Work\\medcoding_file\\SHR-1210-III-306_whoDrug_569540613.zip");
//        MultipartFile multipartFile = FilesUtil.fileToMultipartFile(file);
//        try {
//            minioUtil.uploadFile(multipartFile,"MD5value");
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }


//    @Test
//    void testUpload() {
//        // Minio 服务器信息
//        String endpoint = "http://**********:9000";
//        String accessKey = "minioadmin";
//        String secretKey = "minioadmin";
//        String bucketName = "coding";
//
//        // 初始化Minio客户端
//        MinioClient minioClient = MinioClient.builder()
//                .endpoint(endpoint)
//                .credentials(accessKey, secretKey)
//                .build();
//
//        // 获取本地文件夹
//        File dir = new File("C:\\Work\\medcoding_file");
//        File[] files = dir.listFiles();
//
//        // 遍历上传文件
//        for (File file : files) {
//            String objectName = file.getName();
//            // 构造PutObjectArgs,上传文件
//            PutObjectArgs args = null;
//            try {
//                args = PutObjectArgs.builder()
//                        .bucket(bucketName)
//                        .object(objectName)
//                        .contentType("application/zip")
//                        .stream(new FileInputStream(file), file.length(), -1)
//                        .build();
//            } catch (FileNotFoundException e) {
//                throw new RuntimeException(e);
//            }
//
//            try {
//                minioClient.putObject(args);
//            } catch (ErrorResponseException e) {
//                throw new RuntimeException(e);
//            } catch (InsufficientDataException e) {
//                throw new RuntimeException(e);
//            } catch (InternalException e) {
//                throw new RuntimeException(e);
//            } catch (InvalidKeyException e) {
//                throw new RuntimeException(e);
//            } catch (InvalidResponseException e) {
//                throw new RuntimeException(e);
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            } catch (NoSuchAlgorithmException e) {
//                throw new RuntimeException(e);
//            } catch (ServerException e) {
//                throw new RuntimeException(e);
//            } catch (XmlParserException e) {
//                throw new RuntimeException(e);
//            }
//
//            System.out.println("文件" + objectName + "上传成功");
//        }
//    }

    @Test
    void testShell() {
        Process p = null;
        Process s = null;
        try {
            p = Runtime.getRuntime().exec("cmd /c cd C://Windows//System32 & ipconfig");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        BufferedReader in = new BufferedReader(new InputStreamReader(p.getInputStream()));
        String line;
        while (true) {
            try {
                if (!((line = in.readLine()) != null)) break;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

            // 获取IP信息
            System.out.println(line);

        }
    }


//    @Test
//    void testUnzipFile(){
//        File file = new File("C:\\Work\\medcoding_file\\SHR-1210-II-206_medDrug_679182379.zip");
//        File outFile = null ;   // 输出文件的时候要有文件夹的操作
//        ZipFile zipFile = null;   // 实例化ZipFile对象
//        try {
//            zipFile = new ZipFile(file);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//        ZipInputStream zipInput = null ;    // 定义压缩输入流
//
//        //定义解压的文件名
//        OutputStream out = null ;   // 定义输出流，用于输出每一个实体内容
//        InputStream input = null ;  // 定义输入流，读取每一个ZipEntry
//        ZipEntry entry = null ; // 每一个压缩实体
//        try {
//            zipInput = new ZipInputStream(new FileInputStream(file)) ;  // 实例化ZIpInputStream
//        } catch (FileNotFoundException e) {
//            throw new RuntimeException(e);
//        }
//
//        //遍历压缩包中的文件
//        while(true){
//            try {
//                if (!((entry = zipInput.getNextEntry())!=null)) break;
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            } // 得到一个压缩实体
//            System.out.println("解压缩" + entry.getName() + "文件") ;
//            outFile = new File("C:\\Work\\medcoding_file\\" + entry.getName()) ;   // 定义输出的文件路径
//            if(!outFile.getParentFile().exists()){  // 如果输出文件夹不存在
//                outFile.getParentFile().mkdirs() ;
//                // 创建文件夹 ,如果这里的有多级文件夹不存在,请使用mkdirs()
//                // 如果只是单纯的一级文件夹,使用mkdir()就好了
//            }
//            if(!outFile.exists()){  // 判断输出文件是否存在
//                if(entry.isDirectory())
//                {
//                    outFile.mkdirs();
//                    System.out.println("create directory...");
//                }
//                else
//                {
//                    try {
//                        outFile.createNewFile() ;   // 创建文件
//                    } catch (IOException e) {
//                        throw new RuntimeException(e);
//                    }
//                    System.out.println("create file...");
//                }
//            }
//            if(!entry.isDirectory())
//            {
//                try {
//                    input = zipFile.getInputStream(entry) ; // 得到每一个实体的输入流
//                } catch (IOException e) {
//                    throw new RuntimeException(e);
//                }
//                try {
//                    out = new FileOutputStream(outFile) ;   // 实例化文件输出流
//                } catch (FileNotFoundException e) {
//                    throw new RuntimeException(e);
//                }
//                int temp = 0 ;
//                while(true){
//                    try {
//                        if (!((temp=input.read())!=-1)) break;
//                    } catch (IOException e) {
//                        throw new RuntimeException(e);
//                    }
//                    try {
//                        out.write(temp) ;
//                    } catch (IOException e) {
//                        throw new RuntimeException(e);
//                    }
//                }
//                try {
//                    input.close() ;     // 关闭输入流
//                } catch (IOException e) {
//                    throw new RuntimeException(e);
//                }
//                try {
//                    out.close() ;   // 关闭输出流
//                } catch (IOException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//
//        }
//        try {
//            input.close() ;
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//
//    }

    @Test
    public void splitFileName() {
        String original = "SHR-6390-III-301_W-20191217-0003_系统性抗肿瘤治疗史(1),系统性抗肿瘤治疗史(3),系统性抗肿瘤治疗史(2),系统性抗肿瘤治疗史,系统性抗肿瘤治疗史(4),系统性抗肿瘤治疗史(1)(1),系统性抗肿瘤治疗史(6),系统性抗肿瘤治疗史(5),系统性抗肿瘤治疗史(2)(2)_20230706_2238_CN_EN.csv";
        String[] s = original.split("_");
        String newStr = "";

        if (s.length > 0) {
            String[] split = s[1].split("-");
            s[1] = split[0];
            if (s[2].contains(",")) {
                String[] fileNameSplits = s[2].split(",");
                if (fileNameSplits.length > 0) {
                    //只保留第一个系统性抗肿瘤治疗史(1)
                    String[] fileNameCats = fileNameSplits[0].split("\\(");
                    s[2] = fileNameCats[0];
                }
            }
            newStr = s[0] + "_" + s[1] + "_" + s[2] + ".csv";
        }
        System.out.println(newStr);
    }

    @Test
    public void getAllInfo() {
        String[] arr = new String[]{"SHR3824-302", "SHR3824-301", "SHR-A1811-I-101", "SHR-A1811-I-103", "SHR-A1811-III-301", "SHR-A1921-I-101", "SHR-A2102-I-101", "SHR-4602-I-101", "SHR3162-III-305", "SHR-1316-III-302-EN", "SHR-1210-III-329", "SHR-1210-II-218", "INS068-302", "INS068-301", "HRS9531-201", "HRS9531-102", "HRS-7535-201", "HRS-4642-I-101", "HRS-1167-I-101", "SHR0410-302", "HRS-1167-I-101"};
        String[] ars = new String[]{};
        downloadMedcoding.downloadAllCodingFile(ars);
    }


    @Test
    public void testMail() {
        String mail = "<EMAIL>";
        String regex = "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@" + "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(mail);
        if (matcher.matches()) {
            System.out.println("匹配上了！");
        } else {
            System.out.println("没匹配上了！");
        }
    }

    @Test
    public void testDownLoad() {
        downloadMedcoding.getWhoDrugBatchInfo("INS068-302");
    }


    @Test
    public void getRidOfNasty() {
        String originalString = "SHR-1314-106_M-20240520-0015_1._不良事件_20241105_1024_CN_EN.csv";
        String modifiedString = originalString.replace("1._", "");
        System.out.println(modifiedString);
    }


    @Test
    public void testReadCsv() {
        String inputFile = "C:\\MyFile\\testFile\\med\\FZPL-III-302_M_不良事件.csv"; // CSV 文件路径

        // 读取 CSV 文件
        List<MedCN> dataList = new ArrayList<>();
        EasyExcel.read(inputFile, MedCN.class, new PageReadListener<MedCN>(dataList::addAll)).sheet().doRead();

        // 打印读取的数据
        dataList.forEach(System.out::println);
    }

    @Test
    public void getMedCodingHis() {
        String studyId = "HRFS-Q-2011-301";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "study_coding_plan");
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");

            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String medResult="hrs-5635-201_m-20250918-0023_不良事件_part a_20251009_1545_cn_en.zip*20251092634972209A4956AECE9FD23A479248.zip|hrs-5635-201_m-20250918-0025_既往病史_20251009_1546_cn_en.zip*202510D58693F1C8E84D89A6A6669A871F0ACE.zip|hrs-5635-201_m-20250918-0024_不良事件_part b_20251009_1546_cn_en.zip*2025107A0BE76343054F279228211E11EABD32.zip";
            String whoResult="hrs-5635-201_w-20250918-0029_既往及合并用药_20251009_1546_cn_en.zip*202510C19B861CAD2443B39271DCDE1610350F.zip|hrs-5635-201_w-20250721-0014_慢性乙型肝炎用药(nas药物)_part b_20251009_1546_cn_en.zip*202510930A021EC5474C3591D6C1BB0AE74B16.zip";
            String result = CDTMSAPI.getDataListInfo(token, "coding", "obj.code_doc='" + medResult.toString() + "'" + "and obj.whodrugcoding='"+whoResult.toString()+"'"+"and obj.coder=System", "edit", "");

            log.info(result);


    }


    @Test
    public void getMedFiles(){
        final List<String> codeDoc = FilesUtil.downloadFiles("4076339207", "code_doc", ".zip");
        log.info(codeDoc.toString());
    }

}
