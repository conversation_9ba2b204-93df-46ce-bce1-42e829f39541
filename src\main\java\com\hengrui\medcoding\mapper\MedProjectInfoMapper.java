package com.hengrui.medcoding.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Component
@Mapper
@Repository
@DS("slave")
public interface MedProjectInfoMapper {

    String getAllInfo();


    String getProjectStatus(String projectId);
}
