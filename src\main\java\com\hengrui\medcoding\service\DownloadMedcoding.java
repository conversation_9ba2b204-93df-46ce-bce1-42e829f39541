package com.hengrui.medcoding.service;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hengrui.medcoding.constant.SASOnlieConstant;
import com.hengrui.medcoding.mapper.MedcodingMapper;
import com.hengrui.medcoding.utils.CDTMSAPI;
import com.hengrui.medcoding.utils.FilesUtil;
import com.hengrui.medcoding.utils.MinioUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.Header;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.CookieStore;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.cookie.Cookie;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;
import org.jsoup.internal.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.net.ssl.SSLContext;
import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;


@Component
@Slf4j
public class DownloadMedcoding {

    @Autowired
    MedcodingMapper medcodingMapper;
    @Autowired
    MinioUtil minioUtil;

    //验证环境
    // https://meduap-tst.hengrui.com:8086/usersyn/gettoken?projectid=medcoding_hr&secret=bioknow@228
    //4080 tst 8094 pro
//    public static String PRIFEX_URL = "https://clinical-tst.hengruipharma.com:4080";

         public static String PRIFEX_URL = "https://clinical.hengruipharma.com:8094";
//    public static String PRIFEX_URL = "https://meduap-tst.hengrui.com:8086";


//        public static final String URL = "https://clinical-tst.hengruipharma.com:4080/usersyn/funcstart";
    public static final String URL = "https://clinical.hengruipharma.com:8094/usersyn/funcstart";
//    public static final String URL = "https://meduap-tst.hengrui.com:8086/usersyn/funcstart";

//        public static final String GET_DOWLOAD_URL = "https://clinical-tst.hengruipharma.com:4080/usersyn/funcresult";
        public static final String GET_DOWLOAD_URL = "https://clinical.hengruipharma.com:8094/usersyn/funcresult";
//    public static final String GET_DOWLOAD_URL = "https://meduap-tst.hengrui.com:8086/usersyn/funcresult";
//        public static final String GET_TOKEN_URL = "https://clinical-tst.hengruipharma.com:4080/usersyn/gettoken?projectid=medcoding_hr&secret=bioknow@228";
    public static final String GET_TOKEN_URL = "https://clinical.hengruipharma.com:8094/usersyn/gettoken?projectid=medcoding_hr&secret=bioknow@228";
//    public static final String GET_TOKEN_URL = "https://meduap-tst.hengrui.com:8086/usersyn/gettoken?projectid=medcoding_hr&secret=bioknow@228";
//    public static final String TEMP_FILE_PATH="C:\\Work\\medcoding_file\\";

    public static final String TEMP_FILE_PATH = "/home/<USER>/";

    public static final String MED_TEMP_FILE_PATH = "/home/<USER>/TEMP/";
    public static final String TASKID_PATTERN = "^[A-Z0-9]+$";


    public static final int LOOP = 10;

    public static String getCookies(String url) throws IOException {
        // 全局请求设置
        RequestConfig globalConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.STANDARD).build();
        // 创建cookie store的本地实例
        CookieStore cookieStore = new BasicCookieStore();
        // 创建HttpClient上下文
        HttpClientContext context = HttpClientContext.create();
        context.setCookieStore(cookieStore);

        // 创建一个HttpClient
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultRequestConfig(globalConfig)
                .setDefaultCookieStore(cookieStore).build();

        CloseableHttpResponse res = null;

        // 创建一个get请求用来获取必要的Cookie，如_xsrf信息
        HttpGet get = new HttpGet(url);

        res = httpClient.execute(get, context);
        // 获取常用Cookie,包括_xsrf信息
        StringBuffer cookie = new StringBuffer();
        for (Cookie c : cookieStore.getCookies()) {
            //拼接所有cookie变成一个字符串；
            cookie.append(c.getName() + "=" + c.getValue() + ";");
            log.info(c.getName() + ": " + c.getValue());
        }

        String cookieres = cookie.toString();
        cookieres = cookieres.substring(0, cookieres.length() - 1);
        res.close();
        return cookieres;
    }

    //https post
    public String httpsPostJson(String url, String token, String studyid, String ver, String sn, long importId) {
        String data = "";
        CloseableHttpResponse response = null;
        SSLContext sslContext = null;
        try {
            sslContext = SSLContexts.custom()
                    .loadTrustMaterial(null, new TrustStrategy() {
                        public boolean isTrusted(X509Certificate[] chain, String authType)
                                throws CertificateException {
                            return true;
                        }
                    })
                    .build();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        } catch (KeyStoreException e) {
            throw new RuntimeException(e);
        }
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLContext(sslContext)
                .setSSLHostnameVerifier(new NoopHostnameVerifier())
                .build();
        try {
            List<NameValuePair> params = new ArrayList<>();
            //设置参数
            params.add(new BasicNameValuePair("token", token));
            params.add(new BasicNameValuePair("funcid", "MC_BILINGUAL_REPORT"));
            params.add(new BasicNameValuePair("language", "CN"));
            params.add(new BasicNameValuePair("otherLanguate", "EN"));
            params.add(new BasicNameValuePair("range", "batch"));
            params.add(new BasicNameValuePair("studyid", studyid));
            params.add(new BasicNameValuePair("tableId", "meddraimportdetail"));
            params.add(new BasicNameValuePair("ver", ver));
            params.add(new BasicNameValuePair("sn", sn));
            params.add(new BasicNameValuePair("importId", String.valueOf(importId)));
            params.add(new BasicNameValuePair("outputformat", "excel"));
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setHeader("L", "zh_CN");
            httpPost.setHeader("Token", "");
            httpPost.setHeader("Accept", "application/json, text/plain, */*");
//            httpPost.setHeader("Cookie", "TS012f6883=0188b996a2d0ca2ec517be4b825c18c075f754f1b85f6d0caff051c38fb9177d42b4f87f6e8053c0229fb1902e0af59ad9516f6b76; JSESSIONID=4E25E74DC0BE83A59F6813D194350596; BIGipServer~c_p1~c_clinical_hengruipharma_82~c_pool_clinical_hengruipharma_82=688654858.20992.0000; BIGipServer~c_p1~c_clinical_hengruipharma~c_pool_clinical_hengruipharma=688654858.47873.0000; SL_G_WPT_TO=zh; SL_GWPT_Show_Hide_tmp=1; SL_wptGlobTipTmp=1; TS7ae09111027=08476dfda8ab20009bd70f492752d94438d4732ded1374ccaed05e7c3d542d4ff8a40fa890495cfe08f993457f113000c9a7c56ead2a21c8febed4e25e0060fc90ead8da2a3b9689fa420a3067aa6a2a0073d9f461b655dbeee495fffbf9a9fe; JSESSIONID=23AC46B3159812712D9B823BB031607A; bklang_medcoding_hr=zh; login_sessid=23AC46B3159812712D9B823BB031607A; curr_projectid=medcoding_hr; BIGipServer~c_p1~c_clinical_hengruipharma_8094~c_pool_clinical_hengruipharma_8094=185928202.47873.0000; TSdeb7f5df027=08476dfda8ab2000bd61a3e7be683b2231539b90ea66d9749e64462b7954aa64c347ad110c2740360875175c61113000f9472d44d25d6e7e543f586d803b3ebb744e6a8794f1df1c579320304a159288f5eb1b78c10055b39c7c006d0b905678; token=220d63c1e94e4518a9775e82f42a2302; bioknow_uap_login=true; loginWeb_lang=zh_CN; TS01a882f8=0188b996a27f2e37ee29dabee83bc77015f3b67f23afa2742b1c582c5fcce11c6909ffaa2e2ab232d291dbfd8881bafb5a971dfa8a; TSc4f971ff027=08476dfda8ab2000ed7e2f03d9f447337f8c2691e7a17fae0bfb68f2e267eb8ecbee538d769b90520856e397eb113000ba5831bb3e3940f11294b205637f91c49a314fe3a9825c7b8bdf2b68d307529ef3cbabd114f9fc180d19707778a0b8d2");
            httpPost.setEntity(new UrlEncodedFormEntity(params));
            Header[] allHeaders = httpPost.getAllHeaders();
            response = httpClient.execute(httpPost);
            data = EntityUtils.toString(response.getEntity(), "utf-8");
            EntityUtils.consume(response.getEntity());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                }
            }
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                }
            }
        }
        return data;
    }


    public String httpsGetJson(String url) {
        String data = "";
        CloseableHttpResponse response = null;
        SSLContext sslContext = null;
        try {
            sslContext = SSLContexts.custom()
                    .loadTrustMaterial(null, new TrustStrategy() {
                        public boolean isTrusted(X509Certificate[] chain, String authType)
                                throws CertificateException {
                            return true;
                        }
                    })
                    .build();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        } catch (KeyStoreException e) {
            throw new RuntimeException(e);
        }
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLContext(sslContext)
                .setSSLHostnameVerifier(new NoopHostnameVerifier())
                .build();
        try {
            httpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet(url);
            httpGet.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpGet.setHeader("Accept", "application/json, text/plain, */*");
            Header[] allHeaders = httpGet.getAllHeaders();
            response = httpClient.execute(httpGet);
            int code = response.getStatusLine().getStatusCode();
            data = EntityUtils.toString(response.getEntity(), "utf-8");
            EntityUtils.consume(response.getEntity());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                }
            }
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                }
            }
        }
        return data;
    }


    public String getToken() {
        String data = "";
        CloseableHttpResponse response = null;
        SSLContext sslContext = null;
        try {
            sslContext = SSLContexts.custom()
                    .loadTrustMaterial(null, new TrustStrategy() {
                        public boolean isTrusted(X509Certificate[] chain, String authType)
                                throws CertificateException {
                            return true;
                        }
                    })
                    .build();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        } catch (KeyStoreException e) {
            throw new RuntimeException(e);
        }
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLContext(sslContext)
                .setSSLHostnameVerifier(new NoopHostnameVerifier())
                .build();
        try {
            httpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet(GET_TOKEN_URL);
            httpGet.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpGet.setHeader("Accept", "application/json, text/plain, */*");
            Header[] allHeaders = httpGet.getAllHeaders();
            response = httpClient.execute(httpGet);
            int code = response.getStatusLine().getStatusCode();
            data = EntityUtils.toString(response.getEntity(), "utf-8");
            EntityUtils.consume(response.getEntity());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                }
            }
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                }
            }
        }
        log.info("获取到医学编码系统的token值是:", data);
        return data;
    }

    public String getTaskId(String url, String token, String studyid, String ver, String sn, long importId, Boolean flag) {
        String data = "";
        CloseableHttpResponse response = null;
        SSLContext sslContext = null;
        try {
            sslContext = SSLContexts.custom()
                    .loadTrustMaterial(null, new TrustStrategy() {
                        public boolean isTrusted(X509Certificate[] chain, String authType)
                                throws CertificateException {
                            return true;
                        }
                    })
                    .build();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        } catch (KeyStoreException e) {
            throw new RuntimeException(e);
        }
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLContext(sslContext)
                .setSSLHostnameVerifier(new NoopHostnameVerifier())
                .build();
        try {
            httpClient = HttpClients.createDefault();
            HttpGet httpGet = null;
            //按语言设置下载
            String[] splits = sn.split("-");
            String codingFlag = splits[0];
            DownloadMedcoding.log.info("下载的双语报告的类型是：" + codingFlag);
            String projectLangType = "";
            if (codingFlag.equals("M")) {
                projectLangType = medcodingMapper.getMedProjectLangType(studyid);
            } else if (codingFlag.equals("W")) {
                projectLangType = medcodingMapper.getWhoProjectLangType(studyid);
            }

            if (projectLangType.equals("CN")) {
                //中文项目
                if (flag) {
                    httpGet = new HttpGet(url + "?token=" + token + "&funcid=MC_BILINGUAL_REPORT&language=CN&otherLanguate=EN&range=batch&studyid=" +
                            studyid + "&tableId=meddraimportdetail&ver=" + ver + "&sn=" + sn + "&importId=" + importId + "&outputformat=csv");
                } else {
                    httpGet = new HttpGet(url + "?token=" + token + "&funcid=MC_BILINGUAL_REPORT&language=CN&otherLanguate=EN&range=batch&studyid=" +
                            studyid + "&tableId=whoddimportdetail&ver=" + ver + "&sn=" + sn + "&importId=" + importId + "&outputformat=csv");
                }
            } else if (projectLangType.equals("EN")) {
                //英文项目
                if (flag) {
                    httpGet = new HttpGet(url + "?token=" + token + "&funcid=MC_BILINGUAL_REPORT&language=EN&otherLanguate=CN&range=batch&studyid=" +
                            studyid + "&tableId=meddraimportdetail&ver=" + ver + "&sn=" + sn + "&importId=" + importId + "&outputformat=csv");
                } else {
                    httpGet = new HttpGet(url + "?token=" + token + "&funcid=MC_BILINGUAL_REPORT&language=EN&otherLanguate=CN&range=batch&studyid=" +
                            studyid + "&tableId=whoddimportdetail&ver=" + ver + "&sn=" + sn + "&importId=" + importId + "&outputformat=csv");
                }
            }

            log.info("-------调用的医学编码 任务接口是:" + httpGet.toString());
            httpGet.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpGet.setHeader("Accept", "application/json, text/plain, */*");
            response = httpClient.execute(httpGet);
            data = EntityUtils.toString(response.getEntity(), "utf-8");
            EntityUtils.consume(response.getEntity());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                }
            }
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                }
            }
        }
        log.info("获取到的医学编码系统的taskId是：" + data);
        return data;
    }


    public String getUrl(String url, String token, String taskId) {
        String data = "";
        CloseableHttpResponse response = null;
        SSLContext sslContext = null;
        try {
            sslContext = SSLContexts.custom()
                    .loadTrustMaterial(null, new TrustStrategy() {
                        public boolean isTrusted(X509Certificate[] chain, String authType)
                                throws CertificateException {
                            return true;
                        }
                    })
                    .build();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        } catch (KeyStoreException e) {
            throw new RuntimeException(e);
        }
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLContext(sslContext)
                .setSSLHostnameVerifier(new NoopHostnameVerifier())
                .build();
        try {
            httpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet(url + "?token=" + token + "&funcid=MC_BILINGUAL_REPORT" + "&taskid=" + taskId);
            httpGet.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpGet.setHeader("Accept", "application/json, text/plain, */*");
            Header[] allHeaders = httpGet.getAllHeaders();
            response = httpClient.execute(httpGet);
            int code = response.getStatusLine().getStatusCode();
            data = EntityUtils.toString(response.getEntity(), "utf-8");
            EntityUtils.consume(response.getEntity());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                }
            }
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                }
            }
        }
        return data;
    }

    public String processLongFileName(String originalName) {
        DownloadMedcoding.log.info("输出当前压缩包内的文件名：" + originalName);
        // Remove parentheses from the filename
        originalName = originalName.replace("(", "").replace(")", "");
        originalName = originalName.replace("1._", "");
        String[] s = originalName.split("_");
        String newStr = "";

        if (s.length > 0 && s.length == 7) {
            String[] split = s[1].split("-");
            s[1] = split[0];
            if (s[2].contains(",")) {
                String[] fileNameSplits = s[2].split(",");
                if (fileNameSplits.length > 0) {
                    //只保留第一个系统性抗肿瘤治疗史(1)
                    String[] fileNameCats = fileNameSplits[0].split("\\(");
                    s[2] = fileNameCats[0];
                }
            }
            if (s[0].equals("SHR-A1811-IbII-205")) {
                s[0] = "SHR-A1811-Ib_II-205";
                DownloadMedcoding.log.info("简单改下这个奇怪的项目名：" + s[0]);
            }
            newStr = s[0] + "_" + s[1] + "_" + s[2] + ".csv";
        } else if (s.length > 0 && s.length > 7) {
            String[] split1 = s[1].split("-");
            s[1] = split1[0];
            s[0] = s[0] + "_" + s[1];
            String[] split = s[2].split("-");
            s[1] = split[0];

            if (s[3].contains(",")) {
                String[] fileNameSplits = s[3].split(",");
                if (fileNameSplits.length > 0) {
                    //只保留第一个系统性抗肿瘤治疗史(1)
                    String[] fileNameCats = fileNameSplits[0].split("\\(");
                    s[3] = fileNameCats[0];
                }
            }
            newStr = s[0] + "_" + s[1] + "_" + s[3]  + ".csv";
        }
        newStr = newStr.replaceAll("Ⅱ", "II").replaceAll("Ⅲ", "III").replaceAll("Ⅰ", "I");
        return newStr;
    }

    public String downLoadZipFile(String url, String studyId, String exportTime, String importId) {
        String outputPath = "";
        try (
                CloseableHttpClient httpClient = HttpClients.createDefault();
                CloseableHttpResponse response = httpClient.execute(new HttpGet(PRIFEX_URL + url))) {
            InputStream zipStream = response.getEntity().getContent();
            ZipInputStream zipInput = new ZipInputStream(zipStream);
            File outFile = null;   // 输出文件的时候要有文件夹的操作
            //定义解压的文件名
            OutputStream out = null;   // 定义输出流，用于输出每一个实体内容
            ZipEntry entry = null; // 每一个压缩实体
            String fileNameDatePrefix = "";
            String fileNameDateSuffix = "";
            //遍历压缩包中的文件，文件名小于200才下载，否则不下载
            while (true) {
                try {
                    if (!((entry = zipInput.getNextEntry()) != null)) break;
                } catch (IOException e) {
                    throw new RuntimeException(e);
                } // 得到一个压缩实体
                log.info("解压缩" + entry.getName() + "文件");
                //获取文件里的时间
                 Map<String, String> stringStringMap = extractFileDate(entry.getName());
                fileNameDatePrefix=stringStringMap.get("firstDateTime");
                fileNameDateSuffix=stringStringMap.get("secondDateTime");
                //文件名太长的处理逻辑
                String fileName = processLongFileName(entry.getName());
                if (fileName.contains("SHR-8068-Ⅱ-201-NSCLC")) {
                    fileName = fileName.replace("Ⅱ", "II");
                }
                outFile = new File(TEMP_FILE_PATH + fileName);   // 定义输出的文件路径
                if (!outFile.getParentFile().exists()) {  // 如果输出文件夹不存在
                    outFile.getParentFile().mkdirs();
                    // 创建文件夹 ,如果这里的有多级文件夹不存在,请使用mkdirs()
                    // 如果只是单纯的一级文件夹,使用mkdir()就好了
                }
                if (!outFile.exists()) {  // 判断输出文件是否存在
                    if (entry.isDirectory()) {
                        outFile.mkdirs();
                        log.info("create directory...");
                    } else {
                        try {
                            outFile.createNewFile();   // 创建文件
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                        log.info("create file...");
                    }
                }
                if (!entry.isDirectory()) {
                    try {
                        out = new FileOutputStream(outFile);   // 实例化文件输出流
                    } catch (FileNotFoundException e) {
                        throw new RuntimeException(e);
                    }
                    int temp = 0;
                    while (true) {
                        try {
                            if (!((temp = zipInput.read()) != -1)) break;
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                        try {
                            out.write(temp);
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    }
                    try {
                        out.close();   // 关闭输出流
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }

            }

            try {
                zipInput.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            //如果文件名正常就下载
            MultipartFile multipartFile = FilesUtil.fileToMultipartFile(outFile);
            DownloadMedcoding.log.info("导出的文件名" + outFile.getName());
            DownloadMedcoding.log.info("导出的文件路径" + outFile.getPath());
            outputPath = outFile.getPath();

            //计算文件的MD5编码值
            String md5Hex = DigestUtils.md5Hex(new FileInputStream(outFile.getPath()));
            try {
                minioUtil.uploadFile(multipartFile, md5Hex, studyId, exportTime, importId, fileNameDatePrefix, fileNameDateSuffix);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        } catch (IOException e) {
            log.error("Error downloading file", e);
        }
        return outputPath;
    }


    public String downLoadZipFileNoProcessName(String url, String studyId, String exportTime, String importId) {
        String outputPath = "";
        try (CloseableHttpClient httpClient = HttpClients.createDefault();
             CloseableHttpResponse response = httpClient.execute(new HttpGet(PRIFEX_URL + url))) {

            // 获取最后一个 "/" 之后的子字符串
            String fileName = url.substring(url.lastIndexOf('/') + 1);

            // 对文件名进行 URL 解码
            fileName = URLDecoder.decode(fileName, StandardCharsets.UTF_8.toString());
            log.info("导出的文件名" + fileName);

            // 将响应内容保存到临时文件
            File tempFile = new File(MED_TEMP_FILE_PATH, fileName);
            try (FileOutputStream fos = new FileOutputStream(tempFile);
                 InputStream inputStream = response.getEntity().getContent()) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }

            // 现在可以用这个临时文件来做两个不同的操作
            try (FileInputStream fis1 = new FileInputStream(tempFile);
                 ZipInputStream zipInput1 = new ZipInputStream(fis1)) {
                outputPath = saveZipFile(zipInput1, fileName);
            }

            try (FileInputStream fis2 = new FileInputStream(tempFile)) {
                // 使用新的输入流处理第二个操作
                try (ZipInputStream zipInput2 = new ZipInputStream(fis2)) {
                    File outFile = null;
                    ZipEntry entry;
                    String fileNameDatePrefix = "";
                    String fileNameDateSuffix = "";
                    // 修改这里的循环条件和读取方式
                    while ((entry = zipInput2.getNextEntry()) != null) {
                        log.info("解压缩" + entry.getName() + "文件");
                        //获取文件里的时间
                        Map<String, String> stringStringMap = extractFileDate(entry.getName());
                        fileNameDatePrefix=stringStringMap.get("firstDateTime");
                        fileNameDateSuffix=stringStringMap.get("secondDateTime");
                        fileName = processLongFileName(entry.getName());
                        if (fileName.contains("SHR-8068-Ⅱ-201-NSCLC")) {
                            fileName = fileName.replace("Ⅱ", "II");
                        }

                        outFile = new File(TEMP_FILE_PATH + fileName);
                        if (!outFile.getParentFile().exists()) {
                            outFile.getParentFile().mkdirs();
                        }

                        if (!outFile.exists()) {
                            if (entry.isDirectory()) {
                                outFile.mkdirs();
                                log.info("create directory...");
                            } else {
                                outFile.createNewFile();
                                log.info("create file...");
                            }
                        }

                        if (!entry.isDirectory()) {
                            // 使用缓冲区来读取和写入数据
                            try (FileOutputStream out = new FileOutputStream(outFile)) {
                                byte[] buffer = new byte[4096];
                                int len;
                                while ((len = zipInput2.read(buffer)) > 0) {
                                    out.write(buffer, 0, len);
                                }
                            }
                        }
                        zipInput2.closeEntry(); // 关闭当前entry
                    }

                    zipInput2.close();

                    if (outFile != null) {
                        MultipartFile multipartFile = FilesUtil.fileToMultipartFile(outFile);
                        DownloadMedcoding.log.info("导出的文件名" + outFile.getName());
                        DownloadMedcoding.log.info("导出的文件路径" + outFile.getPath());
                        outputPath = outFile.getPath();

                        // 计算文件的MD5编码值
                        String md5Hex = DigestUtils.md5Hex(new FileInputStream(outFile.getPath()));
                        try {
                            minioUtil.uploadFile(multipartFile, md5Hex, studyId, exportTime, importId,fileNameDatePrefix,fileNameDateSuffix);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            }

            // 操作完成后删除临时文件
            //   tempFile.delete();

            return tempFile.getPath();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    private String saveZipFile(ZipInputStream zipInput, String fileName) throws IOException {
        String outputPath = TEMP_FILE_PATH + fileName; // 保存路径
        try (FileOutputStream fos = new FileOutputStream(outputPath);
             BufferedOutputStream bos = new BufferedOutputStream(fos)) {
            byte[] buffer = new byte[2048];
            int bytesRead;
            while ((bytesRead = zipInput.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
        }
        return outputPath;
    }

    public String waitUrl(String token, String taskId, String studyId, String id, String fileName) {
        final int MAX_RETRIES = 30; // 最大重试次数
        final int BASE_DELAY_MS = 10000; // 基础等待时间10秒
        int retryCount = 0;

        while (retryCount <= MAX_RETRIES) {
            try {
                String urlResult = getUrl(GET_DOWLOAD_URL, token, taskId);
                JSONObject urlObject = JSON.parseObject(urlResult);

                if (urlObject == null) {
                    log.error("获取URL响应解析失败");
                    continue;
                }

                if ("200".equals(urlObject.getString("status"))) {
                    String url = urlObject.getString("url");
                    if (url != null && url.matches("^/+.*$")) {
                        return url;
                    }
                    log.warn("-----medOrWho是" + fileName + "---studyId是" + studyId + "-----导出批次是" + id + "-----token是" + token + "-------taskId是" + taskId + "  获取到无效URL格式: {}", url);
                } else {
                    log.info("检测到token过期,尝试刷新...");
                    token = refreshToken();
                }

                // 指数退避策略
                int delay = (int) (BASE_DELAY_MS * Math.pow(2, retryCount));
                delay = Math.min(delay, 300000); // 最大延迟5分钟
                log.info("等待 {} 秒后重试...", delay / 1000);
                Thread.sleep(delay);

                retryCount++;
            } catch (Exception e) {
                log.error("获取下载URL时发生异常: {}", e.getMessage());
                retryCount++;
            }
        }
        throw new RuntimeException("超过最大重试次数(" + MAX_RETRIES + ") 仍未获取有效下载URL");
    }

    //循环调用接口
    public String waitUrlbak(String token, String taskId, String studyId, String id, String fileName) {
        String resultUrl = "";
        //循环10次，遍历过程中确认是否获取到url
        for (int i = 0; i < LOOP; i++) {
            String urlResult = getUrl(GET_DOWLOAD_URL, token, taskId);
            JSONObject urlObject = JSON.parseObject(urlResult);
            if (urlObject.get("status").equals("200")) {
                String url = (String) urlObject.get("url");
                if (url.matches("^/+.*$")) {
                    resultUrl = url;
                    break;
                } else {
                    log.info("没有下载的url,创建循环，等待..." + studyId);
                    //记录下
                    waitUrlbak(token, taskId, studyId, id, fileName);
                }
            } else {
                //token过期重新获取token
                String tokenResult = getToken();
                JSONObject tokenObject = JSON.parseObject(tokenResult);
                token = (String) tokenObject.get("token");
                urlResult = getUrl(GET_DOWLOAD_URL, token, taskId);
                urlObject = JSON.parseObject(urlResult);
                String url = (String) urlObject.get("url");
                if (url.matches("^/+.*$")) {
                    resultUrl = url;
                    break;
                } else {
                    //记录下
                    log.info("接口状态码非200,没有下载的url,创建循环，等待..." + studyId);
                    waitUrlbak(token, taskId, studyId, id, fileName);
                }
            }
        }
        return resultUrl;
    }

    public void getWhoDrugBatchInfo(String studyId) {
        List<Map<String, String>> whoDrugBatchInfo = medcodingMapper.getWhoDrugBatchInfo(studyId);
        DownloadMedcoding.log.info("查到了" + whoDrugBatchInfo.size() + "条WhoDrug数据");
        if (!whoDrugBatchInfo.isEmpty()) {
            for (int i = 0; i < whoDrugBatchInfo.size(); i++) {
                //调用taskId
                waitTaskId(studyId,
                        whoDrugBatchInfo.get(i).get("COL_VER"), whoDrugBatchInfo.get(i).get("COL_SN"), Long.parseLong(String.valueOf(whoDrugBatchInfo.get(i).get("id"))),
                        whoDrugBatchInfo.get(i).get("exportTime"), false);
            }
        }
    }

    private void downloadProjectFiles(String projectId) {
        // 处理项目ID特殊字符
        if (projectId.equals("SHR-A1811-Ib_II-205")) {
            projectId = "SHR-A1811-Ib/II-205";
        }

        // 下载MedDrug和WhoDrug文件
        getMedDrugBatchInfo(projectId);
        getWhoDrugBatchInfo(projectId);
    }

    private void downloadProjectFilesWithRemoteCallback(String projectId, String token, Map<String, String> remoteStudyMap) {
        String originalProjectId = projectId;

        // 处理项目ID特殊字符
        if (projectId.equals("SHR-A1811-Ib_II-205")) {
            projectId = "SHR-A1811-Ib/II-205";
        }

        // 下载MedDrug和WhoDrug文件 (保持原有逻辑)
        List<String> medFiles = getMedDrugBatchInfoByStudyId2(projectId);
        List<String> whoFiles = getWhoDrugBatchInfoByStudyId2(projectId);

        // 如果没有文件下载成功，跳过远程回填
        if ((medFiles == null || medFiles.isEmpty()) && (whoFiles == null || whoFiles.isEmpty())) {
            log.warn("项目 {} 没有下载到任何文件，跳过远程回填", projectId);
            return;
        }

        // 远程系统回填逻辑
        try {
            String remoteStudyInt = remoteStudyMap.get(projectId);
            if (StringUtils.isEmpty(remoteStudyInt)) {
                // 尝试特殊字符转换
                String alternativeStudyId = originalProjectId.replace("_", "/").replace("/", "_");
                remoteStudyInt = remoteStudyMap.get(alternativeStudyId);
                if (StringUtils.isEmpty(remoteStudyInt)) {
                    log.warn("远端未找到 studyId {}, 跳过回填", projectId);
                    return;
                }
            }

            String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);

            // 创建新记录
            JSONObject param = new JSONObject();
            param.put("studyid", remoteStudyInt);
            param.put("zq", 6);
            String saveRecord = CDTMSAPI.usersyndataSave(token, "coding", formId, "", "", param.toString());
            String dataId = JSON.parseObject(saveRecord).getString("id");

            // 调用远程文件处理逻辑
            pushCodingFilesToRemote(projectId, token, dataId, medFiles, whoFiles);

            log.info("项目 {} 远程回填完成", projectId);
        } catch (Exception e) {
            log.error("项目 {} 远程回填失败: {}", projectId, e.getMessage());
        }
    }

    // 获取CPU核心数
    int coreCount = Runtime.getRuntime().availableProcessors();

    // 根据CPU核心数设置线程池大小
    private final ExecutorService executor = Executors.newFixedThreadPool(coreCount * 2);

    public void downloadAllCodingFile(String[] projectId) {
        log.info("开始异步下载编码文件,项目列表: {}", Arrays.toString(projectId));

        // Get token and remote study mapping once
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");



        List<CompletableFuture<Void>> futures = new ArrayList<>();

        if (projectId.length > 0) {
            // 指定项目异步下载
            for (String id : projectId) {
                // Build remote study mapping
                Map<String, String> remoteStudyMap = new HashMap<>();
                try {

                    String studyIdNum = CDTMSAPI.getDataListInfoWithPage(token, "Xsht", "obj.studyid='" + id + "'" , "edit", "", 100);
                    String studyInfo = CDTMSAPI.getDataListInfoWithPage(token, "xsht", "obj.studyid='" + id + "'", "", "", 100);
                    JSONArray objects = JSON.parseArray(studyIdNum);
                    JSONArray studyObjects = JSON.parseArray(studyInfo);

                    for (int i = 0; i < objects.size(); i++) {
                        String remoteId = objects.getJSONObject(i).getString("id");
                        String remoteStudyId = studyObjects.getJSONObject(i).getString("studyid");
                        remoteStudyMap.put(remoteStudyId, remoteId);
                    }
                    log.info("获取到远端研究映射: {} 条记录", remoteStudyMap.size());
                } catch (Exception e) {
                    log.error("获取远端研究映射失败: {}", e.getMessage());
                }












                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        downloadProjectFilesWithRemoteCallback(id, token, remoteStudyMap);
                    } catch (Exception e) {
                        log.error("项目 {} 下载失败: {}", id, e.getMessage());
                    }
                }, executor);
                futures.add(future);
            }
        } else {
            // 全部项目异步下载
            List<String> allStudyIds = medcodingMapper.getAllStudyIds();
            for (String id : allStudyIds) {
                // Build remote study mapping
                Map<String, String> remoteStudyMap = new HashMap<>();
                try {
                    String studyIdNum = CDTMSAPI.getDataListInfoWithPage(token, "Xsht", "obj.studyid='" + id + "'" , "edit", "", 100);
                    String studyInfo = CDTMSAPI.getDataListInfoWithPage(token, "xsht", "obj.studyid='" + id + "'", "", "", 100);
                    JSONArray objects = JSON.parseArray(studyIdNum);
                    JSONArray studyObjects = JSON.parseArray(studyInfo);

                    for (int i = 0; i < objects.size(); i++) {
                        String remoteId = objects.getJSONObject(i).getString("id");
                        String remoteStudyId = studyObjects.getJSONObject(i).getString("studyid");
                        remoteStudyMap.put(remoteStudyId, remoteId);
                    }
                    log.info("获取到远端研究映射: {} 条记录", remoteStudyMap.size());
                } catch (Exception e) {
                    log.error("获取远端研究映射失败: {}", e.getMessage());
                }




                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        downloadProjectFilesWithRemoteCallback(id, token, remoteStudyMap);
                    } catch (Exception e) {
                        log.error("项目 {} 下载失败: {}", id, e.getMessage());
                    }
                }, executor);
                futures.add(future);
            }
        }

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .exceptionally(ex -> {
                    log.error("下载任务执行异常: {}", ex.getMessage());
                    return null;
                })
                .thenRun(() -> {
                    log.info("所有下载任务已完成");
                    executor.shutdown();
                });
    }

    //获取所有研究id,并调用getMedDrugBatchInfo、getWhoDrugBatchInfo
    public void downloadAllCodingFilebak(String[] projectId) {
        log.info("在下载方法中获取到的命令行参数是:" + Arrays.toString(projectId));
        if (projectId.length > 0) {
            //指定了项目编号
            for (int j = 0; j < projectId.length; j++) {


//                if (ObjectUtils.isEmpty(projectStatus)) {
//                    //不下载
//                    log.info(projectId[j] + "不是活跃项目，不下载报告");
//                } else {
//                    if (projectStatus.equals("40") || projectStatus.equals("50") || projectStatus.equals("60") || projectStatus.equals("70")) {
//                        log.info(projectId[j] + "不是活跃项目，不下载报告");
//                    } else {
//                        getMedDrugBatchInfo(projectId[j]);
//                        getWhoDrugBatchInfo(projectId[j]);
//                    }
//                }
                getMedDrugBatchInfo(projectId[j]);
                getWhoDrugBatchInfo(projectId[j]);

            }
            log.info("指定项目下载完成，关闭当前应用");
            System.exit(0);
        } else {
            //没有指定项目编号，下载全部数据
            List<String> allStudyIds = medcodingMapper.getAllStudyIds();
            for (int i = 0; i < allStudyIds.size(); i++) {
                //判断项目状态
                if (allStudyIds.get(i).equals("SHR-A1811-Ib_II-205")) {
                    allStudyIds.set(i, "SHR-A1811-Ib/II-205");
                }

//                if (ObjectUtils.isEmpty(projectStatus)) {
//                    //不下载
//                    log.info(allStudyIds.get(i) + "不是活跃项目，不下载报告");
//                } else {
//                    if (projectStatus.equals("40") || projectStatus.equals("50") || projectStatus.equals("60") || projectStatus.equals("70")) {
//                        log.info(allStudyIds.get(i) + "不是活跃项目，不下载报告");
//                    } else {
//                        getMedDrugBatchInfo(allStudyIds.get(i));
//                        getWhoDrugBatchInfo(allStudyIds.get(i));
//                    }
//                }
                getMedDrugBatchInfo(allStudyIds.get(i));
                getWhoDrugBatchInfo(allStudyIds.get(i));

            }
            log.info("全部下载完成，关闭当前应用");
            System.exit(0);
        }

    }


    //循环调用获取taskId
    public String waitTaskId(String studyid, String ver, String sn, long importId, String exportTime, Boolean flag) {
        //获取token
        String tokenResult = getToken();
        JSONObject tokenObject = JSON.parseObject(tokenResult);
        String token = (String) tokenObject.get("token");
        String taskIdResult = getTaskId(URL, token, studyid,
                ver, sn, importId,
                flag);
        JSONObject taskIdObject = JSON.parseObject(taskIdResult);
        String taskId = "";
        if (studyid.equals("SHR-8068-Ⅱ-201-NSCLC")) {
            studyid = "SHR-8068-II-201-NSCLC";
        }
        studyid = studyid.replaceAll("Ⅱ", "II").replaceAll("Ⅲ", "III").replaceAll("Ⅰ", "I");
        //判断token是否过期、taskid是否获取到
        if (taskIdObject.get("status").equals("200") && taskIdObject.get("taskid").toString().matches(TASKID_PATTERN)) {
            taskId = (String) taskIdObject.get("taskid");
            if (flag) {
                log.info(studyid + "调用了medDrug下载url接口");
                String medDrugReportUrl = waitUrl(token, taskId, studyid, String.valueOf(importId), "_medDrug_");
                //结束for循环后调用下载命令
                if (!StringUtil.isBlank(medDrugReportUrl)) {
                    log.info(studyid + "调用了medDrug下载url接口,下载地址为：" + medDrugReportUrl);
                    downLoadZipFile(medDrugReportUrl, studyid + "_M", exportTime, String.valueOf(importId));
                }
            } else {
                log.info(studyid + "调用了whoDrug下载url接口");
                String whoDrugReportUrl = waitUrl(token, taskId, studyid, String.valueOf(importId), "_whoDrug_");
                //结束for循环后调用下载命令
                if (!StringUtil.isBlank(whoDrugReportUrl)) {
                    downLoadZipFile(whoDrugReportUrl, studyid + "_W", exportTime, String.valueOf(importId));
                }
            }

        } else {
            taskId = waitTaskId(studyid,
                    ver, sn, importId,
                    exportTime, flag);
        }
        return taskId;
    }

    //获取MedDrug数据
    public void getMedDrugBatchInfo(String studyId) {
        List<Map<String, String>> medDrugBatchInfo = medcodingMapper.getMedDrugBatchInfo(studyId);
        if (!medDrugBatchInfo.isEmpty()) {
            DownloadMedcoding.log.info("查到了" + medDrugBatchInfo.size() + "条MedDrug数据");
            for (int i = 0; i < medDrugBatchInfo.size(); i++) {
                DownloadMedcoding.log.info("studyId是：" + studyId + "id是：" + String.valueOf(medDrugBatchInfo.get(i).get("id")));
                DownloadMedcoding.log.info("COL_VER是：" + medDrugBatchInfo.get(i).get("COL_VER"));
                DownloadMedcoding.log.info("COL_SN是：" + medDrugBatchInfo.get(i).get("COL_SN"));
                DownloadMedcoding.log.info("exportTime是：" + medDrugBatchInfo.get(i).get("exportTime"));

                //调用taskId
                waitTaskId(studyId,
                        medDrugBatchInfo.get(i).get("COL_VER"), medDrugBatchInfo.get(i).get("COL_SN"), Long.parseLong(String.valueOf(medDrugBatchInfo.get(i).get("id"))),
                        medDrugBatchInfo.get(i).get("exportTime"), true);
            }
        }
    }



   /* public Map<String,String> waitTaskIdByStudyId(String studyid, String ver, String sn, long importId, String exportTime, Boolean flag) {
        Map<String,String> result=new HashMap<>();
        //获取token
        String tokenResult = getToken();
        JSONObject tokenObject = JSON.parseObject(tokenResult);
        String token = (String) tokenObject.get("token");
        String taskIdResult = getTaskId(URL, token, studyid,
                ver, sn, importId,
                flag);
        JSONObject taskIdObject = JSON.parseObject(taskIdResult);
        String taskId = "";
        if (studyid.equals("SHR-8068-Ⅱ-201-NSCLC")) {
            studyid = "SHR-8068-II-201-NSCLC";
        }
        studyid = studyid.replaceAll("Ⅱ", "II").replaceAll("Ⅲ", "III").replaceAll("Ⅰ", "I");
        //判断token是否过期、taskid是否获取到
        if (taskIdObject.get("status").equals("200") && taskIdObject.get("taskid").toString().matches(TASKID_PATTERN)) {
            taskId = (String) taskIdObject.get("taskid");
            result.put("taskId",taskId);
            if (flag) {
                log.info(studyid + "调用了medDrug下载url接口");
                String medDrugReportUrl = waitUrl(token, taskId, studyid, String.valueOf(importId), "_medDrug_");
                //结束for循环后调用下载命令
                if (!StringUtil.isBlank(medDrugReportUrl)) {
                    log.info(studyid + "调用了medDrug下载url接口,下载地址为：" + medDrugReportUrl);
                   String outputPath= downLoadZipFile(medDrugReportUrl, studyid + "_M", exportTime);
                    result.put("filePath",outputPath);
                }
            } else {
                log.info(studyid + "调用了whoDrug下载url接口");
                String whoDrugReportUrl = waitUrl(token, taskId, studyid, String.valueOf(importId), "_whoDrug_");
                //结束for循环后调用下载命令
                if (!StringUtil.isBlank(whoDrugReportUrl)) {
                    String outputPath= downLoadZipFile(whoDrugReportUrl, studyid + "_W", exportTime);
                    result.put("filePath",outputPath);
                }
            }

        } else {
            result = waitTaskIdByStudyId(studyid,
                    ver, sn, importId,
                    exportTime, flag);
        }
        return result;
    }*/


    private String refreshToken() {
        String tokenResult = getToken();
        JSONObject tokenObject = JSON.parseObject(tokenResult);
        if (tokenObject == null || !tokenObject.containsKey("token")) {
            throw new RuntimeException("刷新token失败");
        }
        return tokenObject.getString("token");
    }

    /**
     * Check if the files are already uploaded by comparing file names
     * @param result JSON string from remote system
     * @param medResult List of med files
     * @param whoResult List of who files
     * @return true if files already exist, false otherwise
     */
    private boolean isFilesAlreadyUploaded(String result, List<String> medResult, List<String> whoResult) {
        try {
            JSONArray resultArray = JSON.parseArray(result);
            if (resultArray == null || resultArray.isEmpty()) {
                return false;
            }

            // Get the latest record (first one)
            JSONObject latestRecord = resultArray.getJSONObject(0);
            String codeDoc = latestRecord.getString("code_doc");
            String whodrugCoding = latestRecord.getString("whodrugcoding");

            // Extract file names from code_doc and whodrugcoding
            Set<String> existingMedFiles = extractFileNames(codeDoc);
            Set<String> existingWhoFiles = extractFileNames(whodrugCoding);

            // Extract file names from current results
            Set<String> currentMedFiles = medResult.stream()
                .map(this::extractFileName)
                .collect(Collectors.toSet());
            Set<String> currentWhoFiles = whoResult.stream()
                .map(this::extractFileName)
                .collect(Collectors.toSet());

            // Check if all current files already exist in the remote system
            boolean medFilesMatch = existingMedFiles.containsAll(currentMedFiles) &&
                                   currentMedFiles.containsAll(existingMedFiles);
            boolean whoFilesMatch = existingWhoFiles.containsAll(currentWhoFiles) &&
                                   currentWhoFiles.containsAll(existingWhoFiles);

            return medFilesMatch && whoFilesMatch;

        } catch (Exception e) {
            log.error("Error checking if files already uploaded: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Extract file names from a pipe-separated string like "file1.zip*hash1|file2.zip*hash2|"
     */
    private Set<String> extractFileNames(String fileString) {
        if (StringUtils.isEmpty(fileString)) {
            return new HashSet<>();
        }

        return Arrays.stream(fileString.split("\\|"))
            .filter(s -> !s.trim().isEmpty())
            .map(s -> s.split("\\*")[0]) // Get part before the asterisk
            .map(this::extractFileName)
            .collect(Collectors.toSet());
    }

    /**
     * Extract just the filename from a full path
     */
    private String extractFileName(String fullPath) {
        if (StringUtils.isEmpty(fullPath)) {
            return "";
        }

        // Handle both Windows and Unix path separators
        String fileName = fullPath;
        int lastSlash = Math.max(fileName.lastIndexOf('/'), fileName.lastIndexOf('\\'));
        if (lastSlash >= 0) {
            fileName = fileName.substring(lastSlash + 1);
        }

        return fileName;
    }

    public String waitUrlForAPI(String token, String taskId, String studyId, String id, String fileName) {
        String resultUrl = "";
        int retryCount = 0;

        while (retryCount < LOOP) {
            try {
                String urlResult = getUrl(GET_DOWLOAD_URL, token, taskId);
                JSONObject urlObject = JSON.parseObject(urlResult);

                if (!"200".equals(urlObject.get("status"))) {
                    token = refreshToken();
                    retryCount++;
                    continue;
                }

                String url = (String) urlObject.get("url");
                if (url != null && url.matches("^/+.*$")) {
                    resultUrl = url;
                    break;
                }

                log.info("没有下载的 URL, 创建循环，等待..." + studyId);
                retryCount++;

                // 模拟等待
                try {
                    Thread.sleep(10000);
                } catch (InterruptedException e) {
                    log.error("等待时发生中断异常: " + e.getMessage(), e);
                    Thread.currentThread().interrupt();
                    break;
                }
            } catch (Exception e) {
                log.error("获取 URL 时发生异常: " + e.getMessage(), e);
                break;
            }
        }

        return resultUrl;
    }


    public Map<String, String> waitTaskIdByStudyId(String studyid, String ver, String sn, long importId, String exportTime, Boolean flag) {
        Map<String, String> result = new HashMap<>();
        int maxRetries = 30; // 最大重试次数
        int retryInterval = 10; // 重试间隔时间（单位：秒），10 秒

        for (int retry = 0; retry < maxRetries; retry++) {
            // 获取 token
            String tokenResult = getToken();
            JSONObject tokenObject = JSON.parseObject(tokenResult);
            String token = tokenObject.getString("token");

            // 获取 taskId
            String taskIdResult = getTaskId(URL, token, studyid, ver, sn, importId, flag);
            JSONObject taskIdObject = JSON.parseObject(taskIdResult);

            // 处理 studyid 中的特殊字符
            studyid = studyid.replaceAll("Ⅱ", "II").replaceAll("Ⅲ", "III").replaceAll("Ⅰ", "I");

            // 判断 taskId 是否有效
            if (taskIdObject != null && "200".equals(taskIdObject.getString("status")) && taskIdObject.getString("taskid").matches(TASKID_PATTERN)) {
                String taskId = taskIdObject.getString("taskid");
                result.put("taskId", taskId);

                // 根据 flag 调用不同的下载接口
                if (flag) {
                    log.info(studyid + "调用了 medDrug 下载 url 接口");
                    String medDrugReportUrl = waitUrlForAPI(token, taskId, studyid, String.valueOf(importId), "_medDrug_");
                    if (!StringUtil.isBlank(medDrugReportUrl)) {
                        log.info(studyid + "调用了 medDrug 下载 url 接口, 下载地址为：" + medDrugReportUrl);
                        String outputPath = downLoadZipFile(medDrugReportUrl, studyid + "_M", exportTime, String.valueOf(importId));
                        result.put("filePath", outputPath);
                    }
                } else {
                    log.info(studyid + "调用了 whoDrug 下载 url 接口");
                    String whoDrugReportUrl = waitUrlForAPI(token, taskId, studyid, String.valueOf(importId), "_whoDrug_");
                    if (!StringUtil.isBlank(whoDrugReportUrl)) {
                        String outputPath = downLoadZipFile(whoDrugReportUrl, studyid + "_W", exportTime, String.valueOf(importId));
                        result.put("filePath", outputPath);
                    }
                }

                // 如果成功获取 taskId 并执行下载逻辑，直接返回结果
                return result;
            } else {
                // 如果 taskId 无效，等待 5 秒后重试
                log.warn("第 " + (retry + 1) + " 次获取 taskId 失败，等待 " + retryInterval + " 秒后重试...");
                try {
                    Thread.sleep(retryInterval * 1000); // 等待 15 秒
                } catch (InterruptedException e) {
                    log.error("线程睡眠被中断", e);
                    Thread.currentThread().interrupt(); // 恢复中断状态
                }
            }
        }

        // 如果达到最大重试次数仍未成功，返回空结果或抛出异常
        log.error("获取 taskId 失败，已达到最大重试次数 " + maxRetries);
        return result; // 或者抛出异常 throw new RuntimeException("获取 taskId 失败");
    }

    public Map<String, String> waitTaskIdByStudyId2(String studyid, String ver, String sn, long importId, String exportTime, Boolean flag) {
        Map<String, String> result = new HashMap<>();
        int maxRetries = 30; // 最大重试次数
        int retryInterval = 10; // 重试间隔时间（单位：秒），10 秒

        for (int retry = 0; retry < maxRetries; retry++) {
            // 获取 token
            String tokenResult = getToken();
            JSONObject tokenObject = JSON.parseObject(tokenResult);
            String token = tokenObject.getString("token");

            // 获取 taskId
            String taskIdResult = getTaskId(URL, token, studyid, ver, sn, importId, flag);
            JSONObject taskIdObject = JSON.parseObject(taskIdResult);

            // 处理 studyid 中的特殊字符
            studyid = studyid.replaceAll("Ⅱ", "II").replaceAll("Ⅲ", "III").replaceAll("Ⅰ", "I");

            // 判断 taskId 是否有效
            if (taskIdObject != null && "200".equals(taskIdObject.getString("status")) && taskIdObject.getString("taskid").matches(TASKID_PATTERN)) {
                String taskId = taskIdObject.getString("taskid");
                result.put("taskId", taskId);

                // 根据 flag 调用不同的下载接口
                if (flag) {
                    log.info(studyid + "调用了 medDrug 下载 url 接口");
                    String medDrugReportUrl = waitUrlForAPI(token, taskId, studyid, String.valueOf(importId), "_medDrug_");
                    if (!StringUtil.isBlank(medDrugReportUrl)) {
                        log.info(studyid + "调用了 medDrug 下载 url 接口, 下载地址为：" + medDrugReportUrl);
                        String outputPath = downLoadZipFileNoProcessName(medDrugReportUrl, studyid + "_M", exportTime, String.valueOf(importId));
                        result.put("filePath", outputPath);
                    }
                } else {
                    log.info(studyid + "调用了 whoDrug 下载 url 接口");
                    String whoDrugReportUrl = waitUrlForAPI(token, taskId, studyid, String.valueOf(importId), "_whoDrug_");
                    if (!StringUtil.isBlank(whoDrugReportUrl)) {
                        String outputPath = downLoadZipFileNoProcessName(whoDrugReportUrl, studyid + "_W", exportTime, String.valueOf(importId));
                        result.put("filePath", outputPath);
                    }
                }

                // 如果成功获取 taskId 并执行下载逻辑，直接返回结果
                return result;
            } else {
                // 如果 taskId 无效，等待 5 秒后重试
                log.warn("第 " + (retry + 1) + " 次获取 taskId 失败，等待 " + retryInterval + " 秒后重试...");
                try {
                    Thread.sleep(retryInterval * 1000); // 等待 15 秒
                } catch (InterruptedException e) {
                    log.error("线程睡眠被中断", e);
                    Thread.currentThread().interrupt(); // 恢复中断状态
                }
            }
        }

        // 如果达到最大重试次数仍未成功，返回空结果或抛出异常
        log.error("获取 taskId 失败，已达到最大重试次数 " + maxRetries);
        return result; // 或者抛出异常 throw new RuntimeException("获取 taskId 失败");
    }


    public List<String> getMedDrugBatchInfoByStudyId(String studyId) {
        String zipPath = "";
        List<Map<String, String>> medDrugBatchInfo = medcodingMapper.getMedDrugBatchInfo(studyId);
        Map<String, String> downloadInfo = new HashMap<>();
        List<String> downloadFiles = new ArrayList<>();
        if (!medDrugBatchInfo.isEmpty()) {
            DownloadMedcoding.log.info("查到了" + medDrugBatchInfo.size() + "条MedDrug数据");
            for (int i = 0; i < medDrugBatchInfo.size(); i++) {
                DownloadMedcoding.log.info("studyId是：" + studyId + "id是：" + String.valueOf(medDrugBatchInfo.get(i).get("id")));
                DownloadMedcoding.log.info("COL_VER是：" + medDrugBatchInfo.get(i).get("COL_VER"));
                DownloadMedcoding.log.info("COL_SN是：" + medDrugBatchInfo.get(i).get("COL_SN"));
                DownloadMedcoding.log.info("exportTime是：" + medDrugBatchInfo.get(i).get("exportTime"));

                //调用taskId,获取到所有med的输出路径
                downloadInfo = waitTaskIdByStudyId(studyId,
                        medDrugBatchInfo.get(i).get("COL_VER"), medDrugBatchInfo.get(i).get("COL_SN"), Long.parseLong(String.valueOf(medDrugBatchInfo.get(i).get("id"))),
                        medDrugBatchInfo.get(i).get("exportTime"), true);
                if (!ObjectUtils.isEmpty(downloadInfo.get("filePath")) && !downloadInfo.get("filePath").isEmpty()) {
                    downloadFiles.add(downloadInfo.get("filePath"));
                }

            }


            //筛选出所有med的输出路径
            Iterator<String> iterator = downloadFiles.iterator();
            while (iterator.hasNext()) {
                String filePath = iterator.next();
                String matchResult = FilesUtil.fiilterMedFile("同义词库(study)", filePath);
                if ("not found".equals(matchResult)) {
                    iterator.remove(); // 删除不匹配的文件路径
                }
            }


            //打包成zip文件
/*            if(downloadFiles.size()>0){
                String outputZipFile= TEMP_FILE_PATH+studyId+"_M.zip";
                zipPath=outputZipFile;
                try {
                    FilesUtil.packCsvFilesToZip(downloadFiles, outputZipFile);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }*/


        }

        return downloadFiles;

    }


    public List<String> getWhoDrugBatchInfoByStudyId(String studyId) {
        String zipPath = "";
        List<Map<String, String>> whoDrugBatchInfo = medcodingMapper.getWhoDrugBatchInfo(studyId);
        Map<String, String> downloadInfo = new HashMap<>();
        List<String> downloadFiles = new ArrayList<>();
        DownloadMedcoding.log.info("查到了" + whoDrugBatchInfo.size() + "条WhoDrug数据");
        if (!whoDrugBatchInfo.isEmpty()) {
            for (int i = 0; i < whoDrugBatchInfo.size(); i++) {
                //调用taskId
                downloadInfo = waitTaskIdByStudyId(studyId,
                        whoDrugBatchInfo.get(i).get("COL_VER"), whoDrugBatchInfo.get(i).get("COL_SN"), Long.parseLong(String.valueOf(whoDrugBatchInfo.get(i).get("id"))),
                        whoDrugBatchInfo.get(i).get("exportTime"), false);
                if (!ObjectUtils.isEmpty(downloadInfo.get("filePath")) && !downloadInfo.get("filePath").isEmpty()) {
                    downloadFiles.add(downloadInfo.get("filePath"));
                }
            }

            //筛选出所有who的输出路径
            Iterator<String> iterator = downloadFiles.iterator();
            while (iterator.hasNext()) {
                String filePath = iterator.next();
                String matchResult = FilesUtil.fiilterWhoFile("同义词库(study)", filePath);
                if ("not found".equals(matchResult)) {
                    iterator.remove(); // 删除不匹配的文件路径
                }
            }
            //打包成zip文件
/*            if(downloadFiles.size()>0){
                String outputZipFile= TEMP_FILE_PATH+studyId+"_W.zip";
                zipPath=outputZipFile;
                try {
                    FilesUtil.packCsvFilesToZip(downloadFiles, outputZipFile);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }*/

        }

        return downloadFiles;
    }


    public void downloadCodingFileBystudyId(String studyId, String taskId, String projectId) {
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String data = formInfo.get("param");
        String dataId = "";
        if (!data.isEmpty()) {
            JSONObject formInfoData = JSONObject.parseObject(data);
            dataId = formInfoData.get("id").toString();
        }
        log.info("在下载方法中获取到的命令行参数是:" + studyId);
        if (!ObjectUtils.isEmpty(studyId) && !studyId.isEmpty()) {
            List<String> medFiles = getMedDrugBatchInfoByStudyId(studyId);
            List<String> whoFiles = getWhoDrugBatchInfoByStudyId(studyId);
            List<String> medUfns = new ArrayList<>();
            List<String> whoUfns = new ArrayList<>();
          /*  if (medFiles.size() > 0) {
                for (String filePath : medFiles) {
                    String ufn = CDTMSAPI.getUfn(filePath, taskId, projectId);
                    if (!ufn.isEmpty()) {
                        medUfns.add(ufn);
                    }

                }

                cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
                cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
                temp.put("id", dataId);

                // 假设 temp 是一个 Map<String, String>
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < medFiles.size(); i++) {
                    sb.append(medFiles.get(i)) // 添加文件名
                            .append("*")             // 添加分隔符 *
                            .append(medUfns.get(i))  // 添加 ufn
                            .append("|");            // 添加分隔符 |
                }
                temp.put("meddra_file", sb.toString());
                formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
                params.put("data", temp);
                params.put("formId", formId);
                params.put("taskId", taskId);
                params.put("projectId", projectId);
                CDTMSAPI.dataSave(params);
            }

            if (whoFiles.size() > 0) {
                for (String filePath : whoFiles) {
                    String ufn = CDTMSAPI.getUfn(filePath, taskId, projectId);
                    if (!ufn.isEmpty()) {
                        whoUfns.add(ufn);
                    }

                }

                cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
                cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
                temp.put("id", dataId);

                // 假设 temp 是一个 Map<String, String>
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < whoFiles.size(); i++) {
                    sb.append(whoFiles.get(i)) // 添加文件名
                            .append("*")             // 添加分隔符 *
                            .append(whoUfns.get(i))  // 添加 ufn
                            .append("|");            // 添加分隔符 |
                }
                temp.put("whodrug_file", sb.toString());
                formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
                params.put("data", temp);
                params.put("formId", formId);
                params.put("taskId", taskId);
                params.put("projectId", projectId);
                CDTMSAPI.dataSave(params);
            }*/
            log.info("指定项目下载完成");
            JSONObject formInfoData = JSONObject.parseObject(data);
            String codingFile = formInfoData.get("coding_plan").toString();
            log.info("获取到的医学编码计划的文件名称是:" + codingFile);
            if (!ObjectUtils.isEmpty(codingFile) && !codingFile.isEmpty()) {
                String fullfillResult = CDTMSAPI.callCodingFullfill(dataId);
                log.info("-----------------编码文件内容回填计划接口调用的结果是:" + fullfillResult);
            }

        }

    }


    public String getCodingFileIndex(List<String> medFiles, String taskId, String projectId) {
        List<String> medUfns = new ArrayList<>();
        if (medFiles.size() > 0) {
            for (int i = 0; i < medFiles.size(); i++) {
                String ufn = CDTMSAPI.getUfn(medFiles.get(i), taskId, projectId);
                medUfns.add(ufn);
            }

        }

        StringBuilder medResult = new StringBuilder();
        // 遍历列表
        for (int i = 0; i < medFiles.size(); i++) {
            // 处理 strsA 的元素
            medResult.append(medFiles.get(i) != null ? medFiles.get(i) : "")
                    .append("*")
                    .append(medUfns.get(i) != null ? medUfns.get(i) : "");

            // 如果不是最后一个元素，添加分隔符 "|"
            if (i <= medFiles.size() - 1) {
                medResult.append("|");
            }
        }
        return medResult.toString();

    }


    public void downloadCodingFileBystudyId2(String studyId, String taskId, String projectId) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String data = formInfo.get("param");
        String dataId = "";
        String codingDic = "";
        String whodrugcoding = "";
        String codeDoc = "";

        String medResult = "";
        String whoResult = "";
        if (!data.isEmpty()) {
            JSONObject formInfoData = JSONObject.parseObject(data);
            dataId = formInfoData.get("id").toString();
            //编码字典选项获取
            codingDic = formInfoData.get("bmlx").toString();
            whodrugcoding = formInfoData.get("whodrugcoding").toString();
            codeDoc = formInfoData.get("code_doc").toString();
            log.info("获取到表单的编码字典选项是:" + codingDic);
            log.info("获取到表单的MedDRA 结果是:" + codeDoc);
            log.info("获取到表单WHODrug 结果是:" + whodrugcoding);
        }
        log.info("在下载方法中获取到的命令行参数是:" + studyId);
        if (!ObjectUtils.isEmpty(studyId) && !studyId.isEmpty()) {
            //根据编码字典选项获取编码文件
            if (codingDic.equals("MedDRA、WHODrug")) {
                if (!StringUtils.isEmpty(codeDoc)) {
                    //获取当前表单的文件同步至minio
                    List<String> medDRAFilePaths = FilesUtil.downloadFiles(taskId, "code_doc", ".zip");
                    uploadFormMedFileToMinio(medDRAFilePaths, studyId);
                }
//                else {
//                    //获取编码系统导出的文件，同步至mnio
//                    List<String> medFiles = getMedDrugBatchInfoByStudyId2(studyId);
//                    medResult = getCodingFileIndex(medFiles, taskId, projectId);
//                }
                if (!StringUtils.isEmpty(whodrugcoding)) {
                    //获取当前表单的文件同步至minio
                    List<String> WHODrugFilePaths = FilesUtil.downloadFiles(taskId, "whodrugcoding", ".zip");
                    uploadFormMedFileToMinio(WHODrugFilePaths, studyId);
                }


//                else {
//                    //获取编码系统导出的文件，同步至mnio
//                    List<String> whoFiles = getWhoDrugBatchInfoByStudyId2(studyId);
//                    whoResult = getCodingFileIndex(whoFiles, taskId, projectId);
//                }

            } else if (codingDic.equals("MedDRA")) {
                if (!StringUtils.isEmpty(codeDoc)) {
                    //获取当前表单的文件同步至minio
                    List<String> medDRAFilePaths = FilesUtil.downloadFiles(taskId, "code_doc", ".zip");
                    uploadFormMedFileToMinio(medDRAFilePaths, studyId);
                }

//                else {
//                    List<String> medFiles = getMedDrugBatchInfoByStudyId2(studyId);
//                    medResult = getCodingFileIndex(medFiles, taskId, projectId);
//                }

            } else if (codingDic.equals("WHODrug")) {
                if (!StringUtils.isEmpty(whodrugcoding)) {
                    //获取当前表单的文件同步至minio
                    List<String> WHODrugFilePaths = FilesUtil.downloadFiles(taskId, "whodrugcoding", ".zip");
                    //上传到minio
                    uploadFormMedFileToMinio(WHODrugFilePaths, studyId);
                }

//                else {
//                    List<String> whoFiles = getWhoDrugBatchInfoByStudyId2(studyId);
//                    whoResult = getCodingFileIndex(whoFiles, taskId, projectId);
//                }

            }

            cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
            cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
            temp.put("id", dataId);
//            if (codingDic.equals("MedDRA、WHODrug")) {
//                if (StringUtils.isEmpty(codeDoc)) {
//                    temp.put("code_doc", medResult);
//                }
//                if (StringUtils.isEmpty(whodrugcoding)) {
//                    temp.put("whodrugcoding", whoResult);
//                }
//            } else if (codingDic.equals("MedDRA")) {
//                if (StringUtils.isEmpty(codeDoc)) {
//                    temp.put("code_doc", medResult);
//                }
//
//            } else if (codingDic.equals("WHODrug")) {
//                if (StringUtils.isEmpty(whodrugcoding)) {
//                    temp.put("whodrugcoding", whoResult);
//                }
//            }
            // 获取当前系统时间
            LocalDateTime now = LocalDateTime.now();

            // 定义日期时间格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 将时间转换为字符串
            String formattedTime = now.format(formatter);
            temp.put("code_text", "运行时间：" + formattedTime);
            temp.put("zt","05");

            params.put("data", temp);
            String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            params.put("formId", newFormId);
            params.put("taskId", taskId);
            params.put("projectId", projectId);

            CDTMSAPI.dataSave(params);


        }


    }



    public void uploadFormMedFileToMinioNoJudge(List<String> medDRAFilePaths, String studyId) {
        //上传到minio
        String currentTimeStr = FilesUtil.getCurrentTimeStr();

        for (String filePath : medDRAFilePaths) {
            File file = new File(filePath);
            //解压缩zip文件
            if (!file.exists()) {
                log.warn("File not found at path: {}", filePath);
                continue;
            }

            try (FileInputStream fileInputStream = new FileInputStream(file)) {
                try (ZipInputStream zipInput2 = new ZipInputStream(fileInputStream)) {
                    File outFile = null;
                    ZipEntry entry;
                    String fileNameDatePrefix="";
                    String fileNameDateSufix="";
                    // 修改这里的循环条件和读取方式
                    while ((entry = zipInput2.getNextEntry()) != null) {
                        log.info("解压缩" + entry.getName() + "文件");
                        Map<String, String> stringStringMap = extractFileDate(entry.getName());
                        fileNameDatePrefix=stringStringMap.get("firstDateTime");
                        fileNameDateSufix=stringStringMap.get("secondDateTime");

                        String fileName = processLongFileName(entry.getName());
                        if (fileName.contains("SHR-8068-Ⅱ-201-NSCLC")) {
                            fileName = fileName.replace("Ⅱ", "II");
                        }

                        outFile = new File(TEMP_FILE_PATH + fileName);
                        if (!outFile.getParentFile().exists()) {
                            outFile.getParentFile().mkdirs();
                        }

                        if (!outFile.exists()) {
                            if (entry.isDirectory()) {
                                outFile.mkdirs();
                                log.info("create directory...");
                            } else {
                                outFile.createNewFile();
                                log.info("create file...");
                            }
                        }

                        if (!entry.isDirectory()) {
                            // 使用缓冲区来读取和写入数据
                            try (FileOutputStream out = new FileOutputStream(outFile)) {
                                byte[] buffer = new byte[4096];
                                int len;
                                while ((len = zipInput2.read(buffer)) > 0) {
                                    out.write(buffer, 0, len);
                                }
                            }
                        }
                        zipInput2.closeEntry(); // 关闭当前entry
                    }

                    zipInput2.close();

                    if (outFile != null) {
                        MultipartFile multipartFile = FilesUtil.fileToMultipartFile(outFile);
                        DownloadMedcoding.log.info("导出的文件名" + outFile.getName());
                        DownloadMedcoding.log.info("导出的文件路径" + outFile.getPath());
                        String outputPath = outFile.getPath();

                        // 计算文件的MD5编码值
                        String md5Hex = DigestUtils.md5Hex(new FileInputStream(outFile.getPath()));
                        log.info("Processing file - Name: {}, Path: {}, MD5: {}",
                                file.getName(),
                                file.getPath(),
                                md5Hex
                        );
                        try {
                            minioUtil.uploadFileNoJudge(multipartFile, md5Hex, studyId, currentTimeStr, "manual upload", fileNameDatePrefix,fileNameDateSufix);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }

                // 上传成功后删除临时文件
                if (!file.delete()) {
                    log.warn("Failed to delete temporary file: {}", filePath);
                }
            } catch (IOException e) {
                log.error("Error processing file {}: {}", filePath, e.getMessage(), e);
                // 继续处理其他文件而不是直接终止
                continue;
            } catch (Exception e) {
                log.error("Error uploading file {} to MinIO: {}", filePath, e.getMessage(), e);
                // 继续处理其他文件而不是直接终止
                continue;
            }
        }
    }


    public void uploadFormMedFileToMinio(List<String> medDRAFilePaths, String studyId) {
        //上传到minio
        String currentTimeStr = FilesUtil.getCurrentTimeStr();

        for (String filePath : medDRAFilePaths) {
            File file = new File(filePath);
            //解压缩zip文件
            if (!file.exists()) {
                log.warn("File not found at path: {}", filePath);
                continue;
            }

            try (FileInputStream fileInputStream = new FileInputStream(file)) {
                try (ZipInputStream zipInput2 = new ZipInputStream(fileInputStream)) {
                    File outFile = null;
                    ZipEntry entry;
                    String fileNameDatePrefix="";
                    String fileNameDateSufix="";
                    // 修改这里的循环条件和读取方式
                    while ((entry = zipInput2.getNextEntry()) != null) {
                        log.info("解压缩" + entry.getName() + "文件");
                        Map<String, String> stringStringMap = extractFileDate(entry.getName());
                        fileNameDatePrefix=stringStringMap.get("firstDateTime");
                        fileNameDateSufix=stringStringMap.get("secondDateTime");

                        String fileName = processLongFileName(entry.getName());
                        if (fileName.contains("SHR-8068-Ⅱ-201-NSCLC")) {
                            fileName = fileName.replace("Ⅱ", "II");
                        }

                        outFile = new File(TEMP_FILE_PATH + fileName);
                        if (!outFile.getParentFile().exists()) {
                            outFile.getParentFile().mkdirs();
                        }

                        if (!outFile.exists()) {
                            if (entry.isDirectory()) {
                                outFile.mkdirs();
                                log.info("create directory...");
                            } else {
                                outFile.createNewFile();
                                log.info("create file...");
                            }
                        }

                        if (!entry.isDirectory()) {
                            // 使用缓冲区来读取和写入数据
                            try (FileOutputStream out = new FileOutputStream(outFile)) {
                                byte[] buffer = new byte[4096];
                                int len;
                                while ((len = zipInput2.read(buffer)) > 0) {
                                    out.write(buffer, 0, len);
                                }
                            }
                        }
                        zipInput2.closeEntry(); // 关闭当前entry
                    }

                    zipInput2.close();

                    if (outFile != null) {
                        MultipartFile multipartFile = FilesUtil.fileToMultipartFile(outFile);
                        DownloadMedcoding.log.info("导出的文件名" + outFile.getName());
                        DownloadMedcoding.log.info("导出的文件路径" + outFile.getPath());
                        String outputPath = outFile.getPath();

                        // 计算文件的MD5编码值
                        String md5Hex = DigestUtils.md5Hex(new FileInputStream(outFile.getPath()));
                        log.info("Processing file - Name: {}, Path: {}, MD5: {}",
                                file.getName(),
                                file.getPath(),
                                md5Hex
                        );
                        try {
                            minioUtil.uploadFile(multipartFile, md5Hex, studyId, currentTimeStr, "manual upload", fileNameDatePrefix,fileNameDateSufix);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }

                // 上传成功后删除临时文件
                if (!file.delete()) {
                    log.warn("Failed to delete temporary file: {}", filePath);
                }
            } catch (IOException e) {
                log.error("Error processing file {}: {}", filePath, e.getMessage(), e);
                // 继续处理其他文件而不是直接终止
                continue;
            } catch (Exception e) {
                log.error("Error uploading file {} to MinIO: {}", filePath, e.getMessage(), e);
                // 继续处理其他文件而不是直接终止
                continue;
            }
        }
    }


    public List<String> getMedDrugBatchInfoByStudyId2(String studyId) {
        List<Map<String, String>> medDrugBatchInfo = medcodingMapper.getMedDrugBatchInfo(studyId);
        Map<String, String> downloadInfo = new HashMap<>();
        List<String> downloadFiles = new ArrayList<>();
        if (!medDrugBatchInfo.isEmpty()) {
            DownloadMedcoding.log.info("查到了" + medDrugBatchInfo.size() + "条MedDrug数据");
            for (int i = 0; i < medDrugBatchInfo.size(); i++) {
                DownloadMedcoding.log.info("studyId是：" + studyId + "id是：" + String.valueOf(medDrugBatchInfo.get(i).get("id")));
                DownloadMedcoding.log.info("COL_VER是：" + medDrugBatchInfo.get(i).get("COL_VER"));
                DownloadMedcoding.log.info("COL_SN是：" + medDrugBatchInfo.get(i).get("COL_SN"));
                DownloadMedcoding.log.info("exportTime是：" + medDrugBatchInfo.get(i).get("exportTime"));

                //调用taskId,获取到所有med的输出路径
                downloadInfo = waitTaskIdByStudyId2(studyId,
                        medDrugBatchInfo.get(i).get("COL_VER"), medDrugBatchInfo.get(i).get("COL_SN"), Long.parseLong(String.valueOf(medDrugBatchInfo.get(i).get("id"))),
                        medDrugBatchInfo.get(i).get("exportTime"), true);
                if (!ObjectUtils.isEmpty(downloadInfo.get("filePath")) && !downloadInfo.get("filePath").isEmpty()) {
                    downloadFiles.add(downloadInfo.get("filePath"));
                }

            }


        }
        return downloadFiles;
    }


    public List<String> getWhoDrugBatchInfoByStudyId2(String studyId) {
        List<Map<String, String>> whoDrugBatchInfo = medcodingMapper.getWhoDrugBatchInfo(studyId);
        Map<String, String> downloadInfo = new HashMap<>();
        List<String> downloadFiles = new ArrayList<>();
        DownloadMedcoding.log.info("查到了" + whoDrugBatchInfo.size() + "条WhoDrug数据");
        if (!whoDrugBatchInfo.isEmpty()) {
            for (int i = 0; i < whoDrugBatchInfo.size(); i++) {
                //调用taskId
                downloadInfo = waitTaskIdByStudyId2(studyId,
                        whoDrugBatchInfo.get(i).get("COL_VER"), whoDrugBatchInfo.get(i).get("COL_SN"), Long.parseLong(String.valueOf(whoDrugBatchInfo.get(i).get("id"))),
                        whoDrugBatchInfo.get(i).get("exportTime"), false);
                if (!ObjectUtils.isEmpty(downloadInfo.get("filePath")) && !downloadInfo.get("filePath").isEmpty()) {
                    downloadFiles.add(downloadInfo.get("filePath"));
                }
            }
        }

        return downloadFiles;
    }


    private void pushCodingFilesToRemote(String studyId, String token, String dataId,
                                         List<String> medFiles, List<String> whoFiles) {
        try {
            List<String> medUfns = new ArrayList<>();
            List<String> whoUfns = new ArrayList<>();

            // 获取MedDrug文件的UFN
            if (medFiles != null && !medFiles.isEmpty()) {
                for (String filePath : medFiles) {
                    String ufn = CDTMSAPI.getUfnByToken(filePath, dataId, "code_doc", "zip", "coding");
                    medUfns.add(ufn);
                }
            }

            // 获取WhoDrug文件的UFN
            if (whoFiles != null && !whoFiles.isEmpty()) {
                for (String filePath : whoFiles) {
                    String ufn = CDTMSAPI.getUfnByToken(filePath, dataId, "whodrugcoding", "zip", "coding");
                    whoUfns.add(ufn);
                }
            }


            // Get just the filenames
            List<String> medFileNames = medFiles.stream()
                    .map(path -> Paths.get(path).getFileName().toString())
                    .collect(Collectors.toList());



            // Get just the filenames
            List<String> whoFileNames = whoFiles.stream()
                    .map(path -> Paths.get(path).getFileName().toString())
                    .collect(Collectors.toList());

            // 构建文件字符串
            StringBuilder medResult = new StringBuilder();
            if (medFileNames != null && !medFileNames.isEmpty()) {
                for (int i = 0; i < medFileNames.size(); i++) {
                    medResult.append(medFileNames.get(i) != null ? medFileNames.get(i) : "")
                            .append("*")
                            .append(medUfns.get(i) != null ? medUfns.get(i) : "");
                    if (i < medFileNames.size() - 1) {
                        medResult.append("|");
                    }
                }
            }

            StringBuilder whoResult = new StringBuilder();
            if (whoFileNames != null && !whoFileNames.isEmpty()) {
                for (int i = 0; i < whoFileNames.size(); i++) {
                    whoResult.append(whoFileNames.get(i) != null ? whoFileNames.get(i) : "")
                            .append("*")
                            .append(whoUfns.get(i) != null ? whoUfns.get(i) : "");
                    if (i < whoFileNames.size() - 1) {
                        whoResult.append("|");
                    }
                }
            }





            String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "coding",  "obj.studyid='"+studyInt+"'"+"and obj.coder='System' ", "edit", "");

            // Check if the files match with existing records
            if (isFilesAlreadyUploaded(result, medResult, whoResult)) {
                log.info("Files already uploaded for study: " + studyId);
                return; // Skip upload if files already exist
            }


           if( JSON.parseArray(result).size()==0){
               // 准备远程数据
               JSONObject params = new JSONObject();
               // 获取当前系统时间
               LocalDateTime now = LocalDateTime.now();
               DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
               String formattedTime = now.format(formatter);
               String newFormId = CDTMSAPI.getFormIdByTaskId(dataId, "cdtmsen_val");
               params.put("formId", newFormId);
               params.put("zq", "6");
               params.put("zt", "10");
               if(medFiles.size()>0&&whoFiles.size()>0){
                   params.put("bmlx", ",1,2,");
               }else if(medFiles.size()>0){
                   params.put("bmlx", ",1,");
               }else if(whoFiles.size()>0){
                   params.put("bmlx", ",2,");
               }



               params.put("id", dataId);
               params.put("code_doc", medResult.toString());
               params.put("whodrugcoding", whoResult.toString());
               params.put("coder", "System");
               params.put("code_text", "运行时间：" + formattedTime);


               // 保存到远程系统
               String saveResult = CDTMSAPI.usersyndataSave(token, "coding", newFormId, "", "", params.toString());
               log.info("远端写回结果: {}", saveResult);
           }else {
               log.info("该编码报告文件已经上传过CDTMS吗，，无需重复上传: {}", result);
           }






        } catch (Exception e) {
            log.error("推送编码文件到远程系统失败: {}", e.getMessage());
            throw new RuntimeException("远程文件处理失败", e);
        }
    }

    public void downloadCodingFileBystudyId3(String studyId, String token, String dataId) {
        log.info("在下载方法中获取到的命令行参数是:" + studyId);
        if (!ObjectUtils.isEmpty(studyId) && !studyId.isEmpty()) {
            List<String> medFiles = getMedDrugBatchInfoByStudyId2(studyId);
            List<String> whoFiles = getWhoDrugBatchInfoByStudyId2(studyId);
            List<String> medUfns = new ArrayList<>();
            List<String> whoUfns = new ArrayList<>();
            if (medFiles.size() > 0) {
                for (int i = 0; i < medFiles.size(); i++) {
                    String ufn = CDTMSAPI.getUfnByToken(medFiles.get(i), dataId, "code_doc", "zip", "coding");
                    medUfns.add(ufn);
                }

            }

            if (whoFiles.size() > 0) {
                for (int i = 0; i < whoFiles.size(); i++) {
                    String ufn = CDTMSAPI.getUfnByToken(whoFiles.get(i), dataId, "whodrugcoding", "zip", "coding");
                    whoUfns.add(ufn);
                }
            }

            StringBuilder medResult = new StringBuilder();
            StringBuilder whoResult = new StringBuilder();
            // 遍历列表
            for (int i = 0; i < medFiles.size(); i++) {
                // 处理 strsA 的元素
                medResult.append(medFiles.get(i) != null ? medFiles.get(i) : "")
                        .append("*")
                        .append(medUfns.get(i) != null ? medUfns.get(i) : "");

                // 如果不是最后一个元素，添加分隔符 "|"
                if (i <= medFiles.size() - 1) {
                    medResult.append("|");
                }
            }

            for (int i = 0; i < whoFiles.size(); i++) {
                // 处理 strsA 的元素
                whoResult.append(whoFiles.get(i) != null ? whoFiles.get(i) : "")
                        .append("*")
                        .append(whoUfns.get(i) != null ? whoUfns.get(i) : "");

                // 如果不是最后一个元素，添加分隔符 "|"
                if (i <= whoFiles.size() - 1) {
                    whoResult.append("|");
                }
            }


            cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
            cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
            temp.put("id", dataId);
            temp.put("code_doc", medResult.toString());
            temp.put("whodrugcoding", whoResult.toString());
            temp.put("coder", "System");
            // 获取当前系统时间
            LocalDateTime now = LocalDateTime.now();

            // 定义日期时间格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 将时间转换为字符串
            String formattedTime = now.format(formatter);
            temp.put("code_text", "运行时间：" + formattedTime);
            params.put("data", temp);
            String newFormId = CDTMSAPI.getFormIdByTaskId(dataId, "cdtmsen_val");
            params.put("formId", newFormId);
            params.put("zq", 6);

            String saveRecord = CDTMSAPI.usersyndataSave(token, "coding", newFormId, "", "", params.toString());
            log.info("保存结果为:{}", saveRecord);
        }

    }


    public Map<String, String> extractFileDate(String fileName) {
        String firstDateTime = "";
        String secondDateTime = "";
        Map<String, String> result = new HashMap<>();
        // Updated pattern to match the actual filename format
        String pattern = ".*-(\\d{8})-(\\d{4})_.*_(\\d{8})_(\\d{4})_.*";
        Pattern regex = Pattern.compile(pattern);
        Matcher matcher = regex.matcher(fileName);

        if (matcher.matches()) {
            // First datetime: YYYYMMDD-HHMM format
            String firstDate = matcher.group(1); // 20250702
            String firstTime = matcher.group(2); // 0025
            firstDateTime = firstDate.substring(0, 4) + "-" +
                    firstDate.substring(4, 6) + "-" +
                    firstDate.substring(6, 8) + " " +
                    firstTime.substring(0, 2) + ":" +
                    firstTime.substring(2, 4);

            // Second datetime: YYYYMMDD_HHMM format
            String secondDate = matcher.group(3); // 20250715
            String secondTime = matcher.group(4); // 0000
            secondDateTime = secondDate.substring(0, 4) + "-" +
                    secondDate.substring(4, 6) + "-" +
                    secondDate.substring(6, 8) + " " +
                    secondTime.substring(0, 2) + ":" +
                    secondTime.substring(2, 4);

            log.info("First datetime: " + firstDateTime);  // 2025-07-02 00:25
            log.info("Second datetime: " + secondDateTime); // 2025-07-15 00:00
            result.put("firstDateTime", firstDateTime);
            result.put("secondDateTime", secondDateTime);

        } else {
            log.warn("Filename does not match expected pattern: " + fileName);
            result.put("firstDateTime", "");
            result.put("secondDateTime", "");
        }
        return result;
}


}

